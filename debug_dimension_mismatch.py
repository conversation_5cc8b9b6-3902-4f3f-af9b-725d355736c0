#!/usr/bin/env python3
"""
调试维度不匹配问题的脚本
"""

import torch
import traceback
from ultralytics import YOLO
from Globals import *

def debug_model_dimensions():
    """调试模型维度不匹配问题"""
    print("🔍 开始调试维度不匹配问题...")
    print("=" * 60)
    
    try:
        # 1. 尝试加载模型
        print(f"📁 加载模型文件: {model_file}")
        model = YOLO(model_file)
        print("✅ 模型加载成功")
        
        # 2. 检查模型结构
        print(f"\n📊 模型结构信息:")
        print(f"   模型类型: {type(model.model)}")
        
        # 3. 检查模型的各个层
        print(f"\n🔍 检查模型层结构:")
        for i, layer in enumerate(model.model.model):
            print(f"   层 {i}: {type(layer).__name__}")
            if hasattr(layer, 'ch'):
                print(f"        输入通道: {layer.ch}")
            if hasattr(layer, 'nc'):
                print(f"        类别数: {layer.nc}")
        
        # 4. 尝试前向传播
        print(f"\n🚀 测试前向传播...")
        test_input = torch.randn(1, 3, 640, 640)
        
        with torch.no_grad():
            output = model.model(test_input)
            print(f"✅ 前向传播成功")
            print(f"   输出类型: {type(output)}")
            if isinstance(output, (list, tuple)):
                print(f"   输出数量: {len(output)}")
                for i, out in enumerate(output):
                    if isinstance(out, torch.Tensor):
                        print(f"   输出 {i} 形状: {out.shape}")
            elif isinstance(output, torch.Tensor):
                print(f"   输出形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        print(f"\n📋 详细错误信息:")
        traceback.print_exc()
        
        # 尝试分析错误信息
        error_str = str(e)
        if "1280" in error_str and "1344" in error_str:
            print(f"\n💡 分析: 发现1280和1344维度不匹配")
            print(f"   这可能是由于:")
            print(f"   1. 教师模型和学生模型的特征层维度不同")
            print(f"   2. 检测头的输入通道数不匹配")
            print(f"   3. 分类层的输出维度不匹配")
        
        return False

def check_yaml_configuration():
    """检查YAML配置文件"""
    print(f"\n🔧 检查YAML配置...")
    
    try:
        import yaml
        with open(model_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ YAML文件读取成功")
        print(f"   类别数 (nc): {config.get('nc', 'N/A')}")
        
        # 检查head配置
        if 'head' in config:
            print(f"   Head配置:")
            for i, head_layer in enumerate(config['head']):
                print(f"     层 {i}: {head_layer}")
        
        return True
        
    except Exception as e:
        print(f"❌ YAML配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 YOLO模型维度不匹配调试工具")
    print("=" * 60)
    
    # 1. 检查YAML配置
    yaml_ok = check_yaml_configuration()
    
    # 2. 调试模型维度
    model_ok = debug_model_dimensions()
    
    # 3. 总结
    print(f"\n📋 调试总结:")
    print(f"   YAML配置: {'✅ 正常' if yaml_ok else '❌ 异常'}")
    print(f"   模型加载: {'✅ 正常' if model_ok else '❌ 异常'}")
    
    if not model_ok:
        print(f"\n💡 建议的解决方案:")
        print(f"   1. 检查教师模型和学生模型的特征层配置")
        print(f"   2. 确保Detect_Teacher和Detect_Student使用正确的输入层")
        print(f"   3. 验证所有层的通道数配置是否匹配")

if __name__ == "__main__":
    main()
